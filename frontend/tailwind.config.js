/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        'primary': ['Inter', 'system-ui', 'sans-serif'],
        'secondary': ['Inter', 'system-ui', 'sans-serif'],
      },
      colors: {
        // Monochrome design with customizable accent
        'accent': {
          'main': 'var(--accent-color)',
          'dark': 'var(--accent-color-dark)',
          'light': 'var(--accent-color-light)',
        },
        'text': {
          'primary': '#000000',
          'secondary': '#4a4a4a',
          'light': '#8a8a8a',
        },
        'bg': {
          'primary': '#ffffff',
          'secondary': '#f8f9fa',
          'tertiary': '#f1f3f4',
        },
        'border': '#e1e5e9',
      },
      animation: {
        'pulse-slow': 'pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'float': 'float 3s ease-in-out infinite',
      },
      keyframes: {
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        }
      },
      animationDelay: {
        '100': '100ms',
        '200': '200ms',
        '300': '300ms',
        '500': '500ms',
        '700': '700ms',
        '1000': '1000ms',
        '2000': '2000ms',
      }
    },
  },
  plugins: [],
}


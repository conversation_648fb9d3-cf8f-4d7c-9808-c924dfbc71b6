/**
 * HomePage - Landing page with hero section and features
 */
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

// Animated Hero Section
const HeroSection = () => {
  const [mounted, setMounted] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <div className="relative min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-72 h-72 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div className="absolute top-40 right-10 w-72 h-72 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse delay-1000"></div>
        <div className="absolute -bottom-8 left-20 w-72 h-72 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse delay-2000"></div>
      </div>

      {/* Navigation */}
      <nav className="relative z-10 flex justify-between items-center p-6">
        <div className={`text-2xl font-bold text-white transition-all duration-1000 ${mounted ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-10'}`}>
          💬 ChatBot Pro
        </div>
        <button 
          onClick={() => navigate('/login')}
          className={`bg-white text-purple-900 px-6 py-2 rounded-full font-semibold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 ${mounted ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-10'}`}
        >
          Login
        </button>
      </nav>

      {/* Main Content */}
      <div className="relative z-10 flex flex-col items-center justify-center min-h-[80vh] px-4">
        <div className={`text-center max-w-4xl transition-all duration-1000 delay-300 ${mounted ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          <h1 className="text-5xl md:text-7xl font-bold text-white mb-6 leading-tight">
            Intelligent
            <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent"> Chatbot </span>
            System
          </h1>
          
          <p className="text-xl md:text-2xl text-gray-300 mb-8 leading-relaxed">
            Connect with customers across WhatsApp, Facebook, Instagram. 
            <br />
            <span className="text-blue-400">Book tickets • Assign agents • Manage conversations</span>
          </p>

          <div className="flex flex-wrap justify-center gap-4 mb-12">
            <button 
              onClick={() => navigate('/login')}
              className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-8 py-4 rounded-full text-lg font-semibold hover:from-blue-600 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-lg"
            >
              Get Started
            </button>
            <button className="border-2 border-white text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-white hover:text-purple-900 transition-all duration-300 transform hover:scale-105">
              Watch Demo
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Features Section
const FeaturesSection = () => {
  const features = [
    {
      icon: "📱",
      title: "Multi-Platform Support",
      description: "Connect WhatsApp, Facebook Messenger, Instagram DMs in one dashboard",
      color: "from-green-400 to-blue-500"
    },
    {
      icon: "🎫",
      title: "Smart Ticket Booking",
      description: "Automated booking system with real-time availability and confirmations",
      color: "from-purple-400 to-pink-500"
    },
    {
      icon: "👥",
      title: "Agent Management",
      description: "Assign conversations to agents, track performance, and manage workload",
      color: "from-yellow-400 to-orange-500"
    },
    {
      icon: "💬",
      title: "Live Chat Management",
      description: "Real-time conversation handling with AI assistance and smart routing",
      color: "from-blue-400 to-indigo-500"
    },
    {
      icon: "📊",
      title: "Analytics & Reports",
      description: "Detailed insights on customer interactions and agent performance",
      color: "from-teal-400 to-cyan-500"
    },
    {
      icon: "🤖",
      title: "AI-Powered Responses",
      description: "Intelligent auto-replies and conversation suggestions for agents",
      color: "from-red-400 to-pink-500"
    }
  ];

  return (
    <div className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">Powerful Features</h2>
          <p className="text-xl text-gray-600">Everything you need to manage customer conversations</p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div 
              key={index}
              className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group"
            >
              <div className={`w-16 h-16 bg-gradient-to-r ${feature.color} rounded-2xl flex items-center justify-center text-2xl mb-6 group-hover:scale-110 transition-transform duration-300`}>
                {feature.icon}
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">{feature.title}</h3>
              <p className="text-gray-600 leading-relaxed">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Chat Demo Section
const ChatDemoSection = () => {
  const [currentDemo, setCurrentDemo] = useState(0);

  const demos = [
    {
      platform: "WhatsApp",
      icon: "📱",
      color: "bg-green-500",
      messages: [
        { type: "customer", text: "Hi! I need to book a ticket for tomorrow's show", time: "2:30 PM" },
        { type: "bot", text: "Hello! I'd be happy to help you book tickets. Which show are you interested in?", time: "2:30 PM" },
        { type: "customer", text: "The 7 PM comedy show", time: "2:31 PM" },
        { type: "bot", text: "Great choice! I have 3 seats available for the 7 PM comedy show tomorrow. Would you like to proceed?", time: "2:31 PM" }
      ]
    },
    {
      platform: "Facebook",
      icon: "📘",
      color: "bg-blue-600",
      messages: [
        { type: "customer", text: "Is there customer support available?", time: "3:15 PM" },
        { type: "bot", text: "Yes! I'm here to help. I can assist with bookings, account issues, or connect you with a human agent.", time: "3:15 PM" },
        { type: "customer", text: "I need help with my recent order", time: "3:16 PM" },
        { type: "bot", text: "I'm connecting you with Agent Sarah who specializes in order support. Please wait a moment.", time: "3:16 PM" }
      ]
    },
    {
      platform: "Instagram",
      icon: "📷",
      color: "bg-pink-500",
      messages: [
        { type: "customer", text: "Love your latest post! Do you have tickets available?", time: "4:20 PM" },
        { type: "bot", text: "Thank you! Yes, we have tickets available. Which event caught your eye?", time: "4:20 PM" },
        { type: "customer", text: "The weekend music festival", time: "4:21 PM" },
        { type: "bot", text: "Perfect! Weekend passes are $89. I can help you secure your spot right now!", time: "4:21 PM" }
      ]
    }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentDemo((prev) => (prev + 1) % demos.length);
    }, 5000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">See It In Action</h2>
          <p className="text-xl text-gray-600">Real conversation examples across different platforms</p>
        </div>

        <div className="flex justify-center mb-8">
          <div className="flex space-x-4">
            {demos.map((demo, index) => (
              <button
                key={index}
                onClick={() => setCurrentDemo(index)}
                className={`flex items-center space-x-2 px-6 py-3 rounded-full transition-all duration-300 ${
                  currentDemo === index
                    ? `${demo.color} text-white shadow-lg`
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                <span className="text-xl">{demo.icon}</span>
                <span className="font-semibold">{demo.platform}</span>
              </button>
            ))}
          </div>
        </div>

        <div className="max-w-2xl mx-auto">
          <div className="bg-gray-100 rounded-3xl p-6 shadow-xl">
            <div className={`${demos[currentDemo].color} text-white p-4 rounded-t-2xl flex items-center space-x-3`}>
              <span className="text-2xl">{demos[currentDemo].icon}</span>
              <span className="font-bold text-lg">{demos[currentDemo].platform} Chat</span>
            </div>

            <div className="bg-white p-6 rounded-b-2xl space-y-4 max-h-96 overflow-y-auto">
              {demos[currentDemo].messages.map((message, index) => (
                <div key={index} className={`flex ${message.type === 'customer' ? 'justify-end' : 'justify-start'}`}>
                  <div className={`max-w-xs px-4 py-2 rounded-2xl ${
                    message.type === 'customer'
                      ? 'bg-blue-500 text-white rounded-br-sm'
                      : 'bg-gray-200 text-gray-800 rounded-bl-sm'
                  }`}>
                    <p className="text-sm">{message.text}</p>
                    <p className={`text-xs mt-1 ${message.type === 'customer' ? 'text-blue-100' : 'text-gray-500'}`}>
                      {message.time}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Call to Action Section
const CTASection = () => {
  const navigate = useNavigate();

  return (
    <div className="py-20 bg-gradient-to-r from-purple-900 to-indigo-900">
      <div className="max-w-4xl mx-auto text-center px-4">
        <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
          Ready to Transform Your Customer Support?
        </h2>
        <p className="text-xl text-gray-300 mb-8">
          Join thousands of businesses using our chatbot system to provide exceptional customer service
        </p>
        <div className="flex flex-wrap justify-center gap-4">
          <button
            onClick={() => navigate('/login')}
            className="bg-white text-purple-900 px-8 py-4 rounded-full text-lg font-bold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg"
          >
            Start Free Trial
          </button>
          <button className="border-2 border-white text-white px-8 py-4 rounded-full text-lg font-bold hover:bg-white hover:text-purple-900 transition-all duration-300 transform hover:scale-105">
            Schedule Demo
          </button>
        </div>
      </div>
    </div>
  );
};

// Footer
const Footer = () => {
  return (
    <footer className="bg-gray-900 text-white py-12">
      <div className="max-w-7xl mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <h3 className="text-2xl font-bold mb-4">💬 ChatBot Pro</h3>
            <p className="text-gray-400">
              The most advanced chatbot system for modern businesses.
            </p>
          </div>
          <div>
            <h4 className="font-bold mb-4">Features</h4>
            <ul className="space-y-2 text-gray-400">
              <li>Multi-Platform Support</li>
              <li>Ticket Booking</li>
              <li>Agent Management</li>
              <li>Live Chat</li>
            </ul>
          </div>
          <div>
            <h4 className="font-bold mb-4">Platforms</h4>
            <ul className="space-y-2 text-gray-400">
              <li>WhatsApp Business</li>
              <li>Facebook Messenger</li>
              <li>Instagram DMs</li>
              <li>Web Chat</li>
            </ul>
          </div>
          <div>
            <h4 className="font-bold mb-4">Support</h4>
            <ul className="space-y-2 text-gray-400">
              <li>Documentation</li>
              <li>API Reference</li>
              <li>Contact Support</li>
              <li>Community</li>
            </ul>
          </div>
        </div>
        <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
          <p>&copy; 2024 ChatBot Pro. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
};

// Main HomePage Component
const HomePage = () => {
  return (
    <div className="min-h-screen">
      <HeroSection />
      <FeaturesSection />
      <ChatDemoSection />
      <CTASection />
      <Footer />
    </div>
  );
};

export default HomePage;

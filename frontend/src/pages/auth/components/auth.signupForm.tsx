/**
 * Signup form component
 */
import React, { useState } from 'react';
import { User, Mail, Phone, Lock, Eye, EyeOff } from 'lucide-react';
import { Button, Input, Card, CardContent } from '../../../components';
import { RegisterRequest } from '../../../types/api';
import { authService } from '../../../services';

export interface SignupFormProps {
  onSubmit: (userData: RegisterRequest) => void;
  loading?: boolean;
  error?: string | null;
}

const SignupForm: React.FC<SignupFormProps> = ({
  onSubmit,
  loading = false,
  error,
}) => {
  const [formData, setFormData] = useState<RegisterRequest>({
    name: '',
    email: '',
    phone: '',
    password: '',
  });
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    
    if (name === 'confirmPassword') {
      setConfirmPassword(value);
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
    
    // Clear validation error when user starts typing
    if (validationErrors[name]) {
      setValidationErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  // Validate form
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    // Name validation
    if (!formData.name.trim()) {
      errors.name = 'Name is required';
    } else if (formData.name.trim().length < 2) {
      errors.name = 'Name must be at least 2 characters';
    }

    // Email validation
    if (!formData.email) {
      errors.email = 'Email is required';
    } else if (!authService.validateEmail(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }

    // Phone validation
    if (!formData.phone) {
      errors.phone = 'Phone number is required';
    } else if (!authService.validatePhone(formData.phone)) {
      errors.phone = 'Please enter a valid phone number';
    }

    // Password validation
    const passwordValidation = authService.validatePassword(formData.password);
    if (!passwordValidation.isValid) {
      errors.password = passwordValidation.message;
    }

    // Confirm password validation
    if (!confirmPassword) {
      errors.confirmPassword = 'Please confirm your password';
    } else if (formData.password !== confirmPassword) {
      errors.confirmPassword = 'Passwords do not match';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    onSubmit(formData);
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardContent>
        <div className="text-center mb-8">
          <h1 className="text-2xl font-primary font-semibold text-text-primary mb-2">
            Create Account
          </h1>
          <p className="text-text-secondary">
            Sign up to get started with our chat system
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Global error message */}
          {error && (
            <div className="p-3 bg-red-50 border border-error rounded-soft">
              <p className="text-sm text-error">{error}</p>
            </div>
          )}

          {/* Name field */}
          <Input
            type="text"
            name="name"
            label="Full Name"
            placeholder="Enter your full name"
            value={formData.name}
            onChange={handleChange}
            error={validationErrors.name}
            icon={User}
            iconPosition="left"
            fullWidth
            required
            disabled={loading}
          />

          {/* Email field */}
          <Input
            type="email"
            name="email"
            label="Email Address"
            placeholder="Enter your email"
            value={formData.email}
            onChange={handleChange}
            error={validationErrors.email}
            icon={Mail}
            iconPosition="left"
            fullWidth
            required
            disabled={loading}
          />

          {/* Phone field */}
          <Input
            type="tel"
            name="phone"
            label="Phone Number"
            placeholder="Enter your phone number"
            value={formData.phone}
            onChange={handleChange}
            error={validationErrors.phone}
            icon={Phone}
            iconPosition="left"
            fullWidth
            required
            disabled={loading}
          />

          {/* Password field */}
          <div className="relative">
            <Input
              type={showPassword ? 'text' : 'password'}
              name="password"
              label="Password"
              placeholder="Create a password"
              value={formData.password}
              onChange={handleChange}
              error={validationErrors.password}
              icon={Lock}
              iconPosition="left"
              fullWidth
              required
              disabled={loading}
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-9 text-text-light hover:text-text-secondary transition-colors"
              disabled={loading}
            >
              {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
            </button>
          </div>

          {/* Confirm Password field */}
          <div className="relative">
            <Input
              type={showConfirmPassword ? 'text' : 'password'}
              name="confirmPassword"
              label="Confirm Password"
              placeholder="Confirm your password"
              value={confirmPassword}
              onChange={handleChange}
              error={validationErrors.confirmPassword}
              icon={Lock}
              iconPosition="left"
              fullWidth
              required
              disabled={loading}
            />
            <button
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              className="absolute right-3 top-9 text-text-light hover:text-text-secondary transition-colors"
              disabled={loading}
            >
              {showConfirmPassword ? <EyeOff size={16} /> : <Eye size={16} />}
            </button>
          </div>

          {/* Submit button */}
          <Button
            type="submit"
            variant="primary"
            size="lg"
            fullWidth
            loading={loading}
            disabled={loading}
          >
            {loading ? 'Creating Account...' : 'Create Account'}
          </Button>
        </form>

        {/* Additional links */}
        <div className="mt-6 text-center">
          <p className="text-sm text-text-secondary">
            Already have an account?{' '}
            <a
              href="/auth/login"
              className="text-accent-main hover:text-accent-dark font-medium transition-colors"
            >
              Sign in here
            </a>
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default SignupForm;

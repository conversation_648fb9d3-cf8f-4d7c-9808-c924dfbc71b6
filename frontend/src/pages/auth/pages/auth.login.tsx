/**
 * Modern Login Page with Animations and Demo Functionality
 */
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Mail, Lock, Eye, EyeOff, ArrowRight, Sparkles } from 'lucide-react';

const LoginPage: React.FC = () => {
  const navigate = useNavigate();
  const [mounted, setMounted] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Demo credentials
  const demoCredentials = {
    email: '<EMAIL>',
    password: 'demo123'
  };

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email';
    }
    
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    
    setIsLoading(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // Demo login logic
    if (formData.email === demoCredentials.email && formData.password === demoCredentials.password) {
      // Save demo user data
      localStorage.setItem('isAuthenticated', 'true');
      localStorage.setItem('user', JSON.stringify({
        id: 'demo-user-1',
        name: 'Demo User',
        email: formData.email,
        phone: '+1234567890',
        is_active: true,
        created_at: new Date().toISOString(),
        preferences: {}
      }));
      
      // Redirect to dashboard
      navigate('/dashboard');
    } else {
      setErrors({ general: 'Invalid credentials. Use <EMAIL> / demo123' });
    }
    
    setIsLoading(false);
  };

  const fillDemoCredentials = () => {
    setFormData(demoCredentials);
  };

  return (
    <div className="min-h-screen bg-neutral-0 dark:bg-neutral-950 flex items-center justify-center p-4 relative overflow-hidden transition-colors duration-300">
      {/* Subtle background pattern */}
      <div className="absolute inset-0 opacity-30 dark:opacity-20">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-primary-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-primary-600/10 rounded-full blur-3xl"></div>
      </div>

      {/* Main Content */}
      <div className={`relative z-10 w-full max-w-md transition-all duration-700 ease-apple ${mounted ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
        {/* Header */}
        <div className="text-center mb-8 animate-fade-in-up">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-primary-500 dark:bg-primary-600 rounded-2xl mb-6 shadow-medium">
            <Sparkles className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-3xl font-semibold text-neutral-900 dark:text-neutral-0 mb-2">Welcome Back</h1>
          <p className="text-neutral-600 dark:text-neutral-400">Sign in to continue your journey</p>
        </div>

        {/* Demo Banner */}
        <div className="mb-6 p-4 bg-primary-50 dark:bg-primary-900/20 rounded-2xl border border-primary-200 dark:border-primary-800 backdrop-blur-sm animate-slide-in-right">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-neutral-900 dark:text-neutral-100 font-medium">Try Demo Account</p>
              <p className="text-neutral-600 dark:text-neutral-400 text-sm"><EMAIL> / demo123</p>
            </div>
            <button
              onClick={fillDemoCredentials}
              className="px-4 py-2 bg-primary-500 hover:bg-primary-600 text-white rounded-xl transition-all duration-200 text-sm font-medium shadow-soft hover:shadow-medium transform hover:scale-105"
            >
              Fill Demo
            </button>
          </div>
        </div>

        {/* Login Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Global Error */}
          {errors.general && (
            <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-2xl text-red-600 dark:text-red-400 text-sm animate-scale-in">
              {errors.general}
            </div>
          )}

          {/* Email Field */}
          <div className="space-y-2 animate-fade-in-up" style={{ animationDelay: '0.1s' }}>
            <label className="text-neutral-700 dark:text-neutral-300 font-medium text-sm">Email Address</label>
            <div className="relative">
              <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 text-neutral-400 dark:text-neutral-500 w-5 h-5" />
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                className={`w-full pl-12 pr-4 py-4 bg-neutral-50 dark:bg-neutral-800 border ${errors.email ? 'border-red-300 dark:border-red-700' : 'border-neutral-200 dark:border-neutral-700'} rounded-2xl text-neutral-900 dark:text-neutral-100 placeholder-neutral-500 dark:placeholder-neutral-400 focus:outline-none focus:border-primary-500 focus:ring-4 focus:ring-primary-500/10 transition-all duration-200`}
                placeholder="Enter your email"
                disabled={isLoading}
              />
            </div>
            {errors.email && <p className="text-red-500 dark:text-red-400 text-sm animate-slide-in-left">{errors.email}</p>}
          </div>

          {/* Password Field */}
          <div className="space-y-2 animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
            <label className="text-neutral-700 dark:text-neutral-300 font-medium text-sm">Password</label>
            <div className="relative">
              <Lock className="absolute left-4 top-1/2 transform -translate-y-1/2 text-neutral-400 dark:text-neutral-500 w-5 h-5" />
              <input
                type={showPassword ? 'text' : 'password'}
                name="password"
                value={formData.password}
                onChange={handleChange}
                className={`w-full pl-12 pr-12 py-4 bg-neutral-50 dark:bg-neutral-800 border ${errors.password ? 'border-red-300 dark:border-red-700' : 'border-neutral-200 dark:border-neutral-700'} rounded-2xl text-neutral-900 dark:text-neutral-100 placeholder-neutral-500 dark:placeholder-neutral-400 focus:outline-none focus:border-primary-500 focus:ring-4 focus:ring-primary-500/10 transition-all duration-200`}
                placeholder="Enter your password"
                disabled={isLoading}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 text-neutral-400 dark:text-neutral-500 hover:text-neutral-600 dark:hover:text-neutral-300 transition-colors"
                disabled={isLoading}
              >
                {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
              </button>
            </div>
            {errors.password && <p className="text-red-500 dark:text-red-400 text-sm animate-slide-in-left">{errors.password}</p>}
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isLoading}
            className="w-full py-4 bg-primary-500 hover:bg-primary-600 disabled:bg-primary-400 text-white font-semibold rounded-2xl transition-all duration-200 transform hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center space-x-2 shadow-medium hover:shadow-large animate-fade-in-up"
            style={{ animationDelay: '0.3s' }}
          >
            {isLoading ? (
              <>
                <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                <span>Signing In...</span>
              </>
            ) : (
              <>
                <span>Sign In</span>
                <ArrowRight className="w-5 h-5 transition-transform group-hover:translate-x-1" />
              </>
            )}
          </button>
        </form>

        {/* Footer */}
        <div className="mt-8 text-center animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
          <p className="text-neutral-600 dark:text-neutral-400">
            Don't have an account?{' '}
            <button
              onClick={() => navigate('/signup')}
              className="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium transition-colors"
            >
              Sign up here
            </button>
          </p>
        </div>

        {/* Back to Home */}
        <div className="mt-4 text-center animate-fade-in-up" style={{ animationDelay: '0.5s' }}>
          <button
            onClick={() => navigate('/')}
            className="text-neutral-500 dark:text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-200 transition-colors text-sm"
          >
            ← Back to Home
          </button>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;

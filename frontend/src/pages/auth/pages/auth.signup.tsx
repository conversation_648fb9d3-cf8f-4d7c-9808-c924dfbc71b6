/**
 * Signup page with logic and view
 */
import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../../../hooks/redux';
import { registerUser, selectAuth, clearError } from '../../../store/authSlice';
import { RegisterRequest } from '../../../types/api';
import SignupForm from '../components/auth.signupForm';

const SignupPage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { isAuthenticated, isLoading, error } = useAppSelector(selectAuth);

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/chat', { replace: true });
    }
  }, [isAuthenticated, navigate]);

  // Clear error when component unmounts
  useEffect(() => {
    return () => {
      dispatch(clearError());
    };
  }, [dispatch]);

  // Handle signup submission
  const handleSignup = async (userData: RegisterRequest) => {
    try {
      const result = await dispatch(registerUser(userData));
      if (registerUser.fulfilled.match(result)) {
        // Registration successful, navigation will be handled by useEffect
        console.log('Registration successful');
      }
    } catch (error) {
      console.error('Registration error:', error);
    }
  };

  return (
    <div className="min-h-screen bg-bg-primary flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <SignupForm
          onSubmit={handleSignup}
          loading={isLoading}
          error={error}
        />
      </div>
    </div>
  );
};

export default SignupPage;

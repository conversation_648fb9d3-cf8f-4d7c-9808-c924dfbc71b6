/**
 * Chat page - main chat interface
 */
import React, { useState, useEffect, useRef } from 'react';
import { Send, LogOut, User } from 'lucide-react';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { logoutUser, selectAuth } from '../../store/authSlice';
import { Button, Input, Card, CardContent, Loading } from '../../components';
import { chatService } from '../../services';
import { ChatMessage, ChatRequest, ChatResponse } from '../../types/api';

const ChatPage: React.FC = () => {
  const dispatch = useAppDispatch();
  const { user } = useAppSelector(selectAuth);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId, setSessionId] = useState<string>('');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Initialize session ID
  useEffect(() => {
    const newSessionId = chatService.getOrCreateSessionId();
    setSessionId(newSessionId);
  }, []);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Handle logout
  const handleLogout = async () => {
    try {
      await dispatch(logoutUser());
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  // Handle message send
  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const validation = chatService.validateMessage(inputMessage);
    if (!validation.isValid) {
      alert(validation.message);
      return;
    }

    const userMessage: ChatMessage = {
      content: inputMessage,
      message_type: 'user',
      timestamp: new Date().toISOString(),
    };

    // Add user message to chat
    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);

    try {
      const chatRequest: ChatRequest = {
        message: inputMessage,
        session_id: sessionId,
        user_id: user?.id,
      };

      const response = await chatService.sendMessage(chatRequest, {
        onSuccess: (data: ChatResponse) => {
          const assistantMessage: ChatMessage = {
            content: data.response,
            message_type: 'assistant',
            timestamp: new Date().toISOString(),
            analytics: data.assistant_analytics,
          };
          setMessages(prev => [...prev, assistantMessage]);
        },
        onFailure: (error) => {
          const errorMessage: ChatMessage = {
            content: `Sorry, I encountered an error: ${error.message}`,
            message_type: 'assistant',
            timestamp: new Date().toISOString(),
          };
          setMessages(prev => [...prev, errorMessage]);
        },
        onFinally: () => {
          setIsLoading(false);
        },
      });
    } catch (error) {
      setIsLoading(false);
      console.error('Chat error:', error);
    }
  };

  return (
    <div className="min-h-screen bg-bg-primary flex flex-col">
      {/* Header */}
      <header className="bg-bg-secondary border-b border-border p-4">
        <div className="max-w-4xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-accent-main rounded-full flex items-center justify-center">
              <span className="text-white font-semibold text-sm">
                {import.meta.env.VITE_APP_NAME?.charAt(0) || 'C'}
              </span>
            </div>
            <h1 className="text-xl font-primary font-semibold text-text-primary">
              {import.meta.env.VITE_APP_NAME || 'Chat System'}
            </h1>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2 text-text-secondary">
              <User size={16} />
              <span className="text-sm">{user?.name}</span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              icon={LogOut}
              onClick={handleLogout}
            >
              Logout
            </Button>
          </div>
        </div>
      </header>

      {/* Chat Messages */}
      <main className="flex-1 overflow-hidden">
        <div className="max-w-4xl mx-auto h-full flex flex-col">
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {messages.length === 0 ? (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-accent-light rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white text-2xl">💬</span>
                </div>
                <h3 className="text-lg font-primary font-semibold text-text-primary mb-2">
                  Start a conversation
                </h3>
                <p className="text-text-secondary">
                  Send a message to begin chatting with our AI assistant
                </p>
              </div>
            ) : (
              messages.map((message, index) => (
                <div
                  key={index}
                  className={`flex ${message.message_type === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <Card
                    className={`max-w-xs sm:max-w-md lg:max-w-lg ${
                      message.message_type === 'user'
                        ? 'bg-accent-main text-white'
                        : 'bg-bg-secondary'
                    }`}
                    padding="sm"
                  >
                    <CardContent>
                      <p className="text-sm whitespace-pre-wrap">
                        {message.content}
                      </p>
                      <p className={`text-xs mt-2 ${
                        message.message_type === 'user'
                          ? 'text-accent-light'
                          : 'text-text-light'
                      }`}>
                        {chatService.formatMessageTime(message.timestamp)}
                      </p>
                    </CardContent>
                  </Card>
                </div>
              ))
            )}
            
            {isLoading && (
              <div className="flex justify-start">
                <Card className="bg-bg-secondary max-w-xs" padding="sm">
                  <CardContent>
                    <Loading variant="dots" size="sm" />
                  </CardContent>
                </Card>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>

          {/* Message Input */}
          <div className="border-t border-border p-4">
            <form onSubmit={handleSendMessage} className="flex space-x-3">
              <Input
                type="text"
                placeholder="Type your message..."
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                fullWidth
                disabled={isLoading}
                className="flex-1"
              />
              <Button
                type="submit"
                variant="primary"
                icon={Send}
                disabled={!inputMessage.trim() || isLoading}
                loading={isLoading}
              >
                Send
              </Button>
            </form>
          </div>
        </div>
      </main>
    </div>
  );
};

export default ChatPage;

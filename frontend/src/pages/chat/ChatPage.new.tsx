/**
 * Modern Chat Page - Pinterest-inspired design
 */
import React, { useState, useEffect, useRef } from 'react';
import { Send, Paperclip, Smile, MoreVertical, Phone, Video, Search } from 'lucide-react';
import { MessageSendAnimation, MessageReceiveAnimation, TypingIndicator, useChatAnimations } from '../../components/ChatAnimations';
import { ThemeToggle } from '../../components';

interface Message {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: Date;
  status?: 'sending' | 'sent' | 'delivered' | 'read';
}

const ChatPage: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      content: 'Hello! How can I help you today?',
      isUser: false,
      timestamp: new Date(Date.now() - 300000),
      status: 'read'
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { animatingMessage, showTyping, sendMessage, receiveMessage } = useChatAnimations();

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, showTyping]);

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const newMessage: Message = {
      id: Date.now().toString(),
      content: inputMessage,
      isUser: true,
      timestamp: new Date(),
      status: 'sending'
    };

    setMessages(prev => [...prev, newMessage]);
    const messageToSend = inputMessage;
    setInputMessage('');
    setIsLoading(true);

    // Trigger send animation
    sendMessage(messageToSend);

    // Simulate API call
    try {
      // Update message status to sent
      setTimeout(() => {
        setMessages(prev => prev.map(msg => 
          msg.id === newMessage.id ? { ...msg, status: 'sent' } : msg
        ));
      }, 1000);

      // Simulate AI response
      setTimeout(() => {
        receiveMessage();
        const aiResponse: Message = {
          id: (Date.now() + 1).toString(),
          content: `I understand you said: "${messageToSend}". How can I assist you further?`,
          isUser: false,
          timestamp: new Date(),
          status: 'read'
        };
        setMessages(prev => [...prev, aiResponse]);
        setIsLoading(false);
      }, 2500);

    } catch (error) {
      console.error('Error sending message:', error);
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="flex flex-col h-screen bg-neutral-0 dark:bg-neutral-950">
      {/* Chat Header */}
      <div className="flex items-center justify-between p-4 border-b border-neutral-200 dark:border-neutral-800 bg-neutral-50 dark:bg-neutral-900">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full flex items-center justify-center">
            <span className="text-white font-semibold">AI</span>
          </div>
          <div>
            <h3 className="font-semibold text-neutral-900 dark:text-neutral-100">AI Assistant</h3>
            <p className="text-sm text-neutral-500 dark:text-neutral-400">Online</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <button className="p-2 hover:bg-neutral-200 dark:hover:bg-neutral-700 rounded-xl transition-colors">
            <Phone className="w-5 h-5 text-neutral-600 dark:text-neutral-400" />
          </button>
          <button className="p-2 hover:bg-neutral-200 dark:hover:bg-neutral-700 rounded-xl transition-colors">
            <Video className="w-5 h-5 text-neutral-600 dark:text-neutral-400" />
          </button>
          <button className="p-2 hover:bg-neutral-200 dark:hover:bg-neutral-700 rounded-xl transition-colors">
            <Search className="w-5 h-5 text-neutral-600 dark:text-neutral-400" />
          </button>
          <ThemeToggle />
          <button className="p-2 hover:bg-neutral-200 dark:hover:bg-neutral-700 rounded-xl transition-colors">
            <MoreVertical className="w-5 h-5 text-neutral-600 dark:text-neutral-400" />
          </button>
        </div>
      </div>

      {/* Messages Container */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div key={message.id} className={`flex ${message.isUser ? 'justify-end' : 'justify-start'} animate-fade-in-up`}>
            <div className={`
              max-w-xs lg:max-w-md px-4 py-3 rounded-2xl shadow-soft
              ${message.isUser 
                ? 'bg-primary-500 text-white rounded-br-md' 
                : 'bg-neutral-100 dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100 rounded-bl-md'
              }
              transition-all duration-200 hover:shadow-medium
            `}>
              <p className="text-sm leading-relaxed">{message.content}</p>
              <div className="flex items-center justify-between mt-2">
                <span className={`text-xs ${message.isUser ? 'text-primary-100' : 'text-neutral-500 dark:text-neutral-400'}`}>
                  {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </span>
                {message.isUser && (
                  <div className="flex space-x-1">
                    <div className={`w-1 h-1 rounded-full ${
                      message.status === 'read' ? 'bg-primary-200' : 
                      message.status === 'delivered' ? 'bg-primary-300' : 
                      message.status === 'sent' ? 'bg-primary-400' : 'bg-primary-500'
                    }`}></div>
                    <div className={`w-1 h-1 rounded-full ${
                      message.status === 'read' ? 'bg-primary-200' : 
                      message.status === 'delivered' ? 'bg-primary-300' : 'bg-transparent'
                    }`}></div>
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}

        {/* Animated Messages */}
        {animatingMessage && (
          <MessageSendAnimation 
            message={animatingMessage} 
            onAnimationComplete={() => {}}
          />
        )}

        {showTyping && <TypingIndicator />}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <div className="p-4 border-t border-neutral-200 dark:border-neutral-800 bg-neutral-50 dark:bg-neutral-900">
        <div className="flex items-end space-x-3">
          <button className="p-2 hover:bg-neutral-200 dark:hover:bg-neutral-700 rounded-xl transition-colors">
            <Paperclip className="w-5 h-5 text-neutral-600 dark:text-neutral-400" />
          </button>
          
          <div className="flex-1 relative">
            <textarea
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type a message..."
              className="w-full px-4 py-3 bg-neutral-100 dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-2xl text-neutral-900 dark:text-neutral-100 placeholder-neutral-500 dark:placeholder-neutral-400 focus:outline-none focus:border-primary-500 focus:ring-4 focus:ring-primary-500/10 transition-all duration-200 resize-none"
              rows={1}
              style={{ minHeight: '44px', maxHeight: '120px' }}
              disabled={isLoading}
            />
          </div>

          <button className="p-2 hover:bg-neutral-200 dark:hover:bg-neutral-700 rounded-xl transition-colors">
            <Smile className="w-5 h-5 text-neutral-600 dark:text-neutral-400" />
          </button>

          <button
            onClick={handleSendMessage}
            disabled={!inputMessage.trim() || isLoading}
            className="p-3 bg-primary-500 hover:bg-primary-600 disabled:bg-neutral-300 dark:disabled:bg-neutral-700 text-white rounded-2xl transition-all duration-200 transform hover:scale-105 disabled:transform-none disabled:cursor-not-allowed shadow-medium hover:shadow-large"
          >
            <Send className="w-5 h-5" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChatPage;

/**
 * CTA Page - Pinterest-inspired clean design
 * Reference: https://www.pinterest.com/pin/7740630604822748/
 */
import React, { useState, useEffect } from 'react';
import { 
  Calendar, 
  Clock, 
  User, 
  Mail, 
  Phone, 
  CheckCircle, 
  ArrowRight,
  Star,
  MapPin,
  CreditCard,
  Shield,
  Zap,
  Users,
  Award
} from 'lucide-react';
import { ThemeToggle } from '../../components';

interface TimeSlot {
  id: string;
  date: string;
  time: string;
  datetime: string;
  available: boolean;
  max_bookings: number;
  current_bookings: number;
  available_spots: number;
}

interface BookingForm {
  name: string;
  email: string;
  phone: string;
  service_type: string;
  date: string;
  time: string;
  notes: string;
}

const CTAPage: React.FC = () => {
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([]);
  const [selectedSlot, setSelectedSlot] = useState<TimeSlot | null>(null);
  const [bookingForm, setBookingForm] = useState<BookingForm>({
    name: '',
    email: '',
    phone: '',
    service_type: 'general_consultation',
    date: '',
    time: '',
    notes: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchAvailableSlots();
  }, []);

  const fetchAvailableSlots = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/booking/slots?days_ahead=14');
      if (response.ok) {
        const slots = await response.json();
        setTimeSlots(slots);
      }
    } catch (error) {
      console.error('Error fetching time slots:', error);
    }
  };

  const handleSlotSelect = (slot: TimeSlot) => {
    setSelectedSlot(slot);
    setBookingForm(prev => ({
      ...prev,
      date: slot.date,
      time: slot.time
    }));
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setBookingForm(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedSlot) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('http://localhost:8000/api/booking/book', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...bookingForm,
          session_id: 'web-booking-' + Date.now()
        })
      });

      if (response.ok) {
        setIsSubmitted(true);
      } else {
        const errorData = await response.json();
        setError(errorData.detail || 'Failed to create booking');
      }
    } catch (error) {
      setError('Network error. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const serviceTypes = [
    { value: 'general_consultation', label: 'General Consultation' },
    { value: 'technical_support', label: 'Technical Support' },
    { value: 'course_enrollment', label: 'Course Enrollment' },
    { value: 'loksewa_preparation', label: 'Loksewa Preparation' },
    { value: 'career_guidance', label: 'Career Guidance' }
  ];

  const features = [
    { icon: Zap, title: 'Instant Booking', description: 'Book appointments in seconds' },
    { icon: Shield, title: 'Secure & Private', description: 'Your data is protected' },
    { icon: Users, title: 'Expert Support', description: 'Professional guidance' },
    { icon: Award, title: 'Proven Results', description: '95% satisfaction rate' }
  ];

  if (isSubmitted) {
    return (
      <div className="min-h-screen bg-neutral-50 dark:bg-neutral-950 flex items-center justify-center p-4">
        <div className="max-w-md w-full text-center animate-fade-in-up">
          <div className="bg-white dark:bg-neutral-900 rounded-3xl p-8 border border-neutral-200 dark:border-neutral-800 shadow-lg">
            <div className="w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-6">
              <CheckCircle className="w-8 h-8 text-green-600 dark:text-green-400" />
            </div>
            <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">
              Booking Confirmed!
            </h2>
            <p className="text-neutral-600 dark:text-neutral-400 mb-6">
              Your appointment has been successfully scheduled. You'll receive a confirmation email shortly.
            </p>
            <button
              onClick={() => {
                setIsSubmitted(false);
                setBookingForm({
                  name: '',
                  email: '',
                  phone: '',
                  service_type: 'general_consultation',
                  date: '',
                  time: '',
                  notes: ''
                });
                setSelectedSlot(null);
              }}
              className="w-full py-3 bg-neutral-900 dark:bg-neutral-100 text-neutral-100 dark:text-neutral-900 rounded-2xl font-semibold hover:bg-neutral-800 dark:hover:bg-neutral-200 transition-colors"
            >
              Book Another Appointment
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-neutral-50 dark:bg-neutral-950">
      {/* Header */}
      <div className="bg-white dark:bg-neutral-900 border-b border-neutral-200 dark:border-neutral-800">
        <div className="max-w-7xl mx-auto px-4 py-4 flex justify-between items-center">
          <h1 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100">
            Book Appointment
          </h1>
          <ThemeToggle />
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Column - Hero & Features */}
          <div className="space-y-8">
            {/* Hero Section */}
            <div className="bg-white dark:bg-neutral-900 rounded-3xl p-8 border border-neutral-200 dark:border-neutral-800 animate-fade-in-up">
              <div className="text-center mb-8">
                <h2 className="text-4xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">
                  Schedule Your
                  <span className="block bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                    Consultation
                  </span>
                </h2>
                <p className="text-lg text-neutral-600 dark:text-neutral-400 max-w-md mx-auto">
                  Get expert guidance and support tailored to your needs. Book your appointment today.
                </p>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-3 gap-4 mb-8">
                {[
                  { number: '500+', label: 'Happy Clients' },
                  { number: '95%', label: 'Success Rate' },
                  { number: '24/7', label: 'Support' }
                ].map((stat, index) => (
                  <div key={index} className="text-center p-4 bg-neutral-50 dark:bg-neutral-800 rounded-2xl">
                    <div className="text-2xl font-bold text-neutral-900 dark:text-neutral-100">
                      {stat.number}
                    </div>
                    <div className="text-sm text-neutral-600 dark:text-neutral-400">
                      {stat.label}
                    </div>
                  </div>
                ))}
              </div>

              {/* Features */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {features.map((feature, index) => (
                  <div key={index} className="flex items-start space-x-3 p-4 rounded-2xl hover:bg-neutral-50 dark:hover:bg-neutral-800 transition-colors">
                    <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-xl flex items-center justify-center flex-shrink-0">
                      <feature.icon className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-neutral-900 dark:text-neutral-100 mb-1">
                        {feature.title}
                      </h3>
                      <p className="text-sm text-neutral-600 dark:text-neutral-400">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Column - Booking Form */}
          <div className="space-y-6">
            {/* Time Slots */}
            <div className="bg-white dark:bg-neutral-900 rounded-3xl p-6 border border-neutral-200 dark:border-neutral-800 animate-fade-in-up" style={{ animationDelay: '0.1s' }}>
              <h3 className="text-xl font-semibold text-neutral-900 dark:text-neutral-100 mb-4 flex items-center">
                <Calendar className="w-5 h-5 mr-2" />
                Select Time Slot
              </h3>
              
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-3 max-h-64 overflow-y-auto">
                {timeSlots.slice(0, 12).map((slot) => (
                  <button
                    key={slot.id}
                    onClick={() => handleSlotSelect(slot)}
                    disabled={!slot.available}
                    className={`p-3 rounded-xl text-sm font-medium transition-all duration-200 ${
                      selectedSlot?.id === slot.id
                        ? 'bg-neutral-900 dark:bg-neutral-100 text-neutral-100 dark:text-neutral-900'
                        : slot.available
                        ? 'bg-neutral-100 dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100 hover:bg-neutral-200 dark:hover:bg-neutral-700'
                        : 'bg-neutral-50 dark:bg-neutral-900 text-neutral-400 dark:text-neutral-600 cursor-not-allowed'
                    }`}
                  >
                    <div className="flex items-center justify-center mb-1">
                      <Clock className="w-4 h-4 mr-1" />
                      {slot.time}
                    </div>
                    <div className="text-xs opacity-75">
                      {new Date(slot.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                    </div>
                    {slot.available && (
                      <div className="text-xs mt-1 opacity-60">
                        {slot.available_spots} spots left
                      </div>
                    )}
                  </button>
                ))}
              </div>
            </div>

            {/* Booking Form */}
            <div className="bg-white dark:bg-neutral-900 rounded-3xl p-6 border border-neutral-200 dark:border-neutral-800 animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
              <h3 className="text-xl font-semibold text-neutral-900 dark:text-neutral-100 mb-4 flex items-center">
                <User className="w-5 h-5 mr-2" />
                Your Information
              </h3>

              {error && (
                <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl text-red-600 dark:text-red-400 text-sm">
                  {error}
                </div>
              )}

              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                      Full Name
                    </label>
                    <input
                      type="text"
                      name="name"
                      value={bookingForm.name}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 bg-neutral-100 dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl text-neutral-900 dark:text-neutral-100 placeholder-neutral-500 dark:placeholder-neutral-400 focus:outline-none focus:border-neutral-300 dark:focus:border-neutral-600 transition-colors"
                      placeholder="Enter your name"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                      Email Address
                    </label>
                    <input
                      type="email"
                      name="email"
                      value={bookingForm.email}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 bg-neutral-100 dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl text-neutral-900 dark:text-neutral-100 placeholder-neutral-500 dark:placeholder-neutral-400 focus:outline-none focus:border-neutral-300 dark:focus:border-neutral-600 transition-colors"
                      placeholder="Enter your email"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      name="phone"
                      value={bookingForm.phone}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 bg-neutral-100 dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl text-neutral-900 dark:text-neutral-100 placeholder-neutral-500 dark:placeholder-neutral-400 focus:outline-none focus:border-neutral-300 dark:focus:border-neutral-600 transition-colors"
                      placeholder="Enter your phone"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                      Service Type
                    </label>
                    <select
                      name="service_type"
                      value={bookingForm.service_type}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 bg-neutral-100 dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl text-neutral-900 dark:text-neutral-100 focus:outline-none focus:border-neutral-300 dark:focus:border-neutral-600 transition-colors"
                    >
                      {serviceTypes.map((service) => (
                        <option key={service.value} value={service.value}>
                          {service.label}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                    Additional Notes (Optional)
                  </label>
                  <textarea
                    name="notes"
                    value={bookingForm.notes}
                    onChange={handleInputChange}
                    rows={3}
                    className="w-full px-4 py-3 bg-neutral-100 dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl text-neutral-900 dark:text-neutral-100 placeholder-neutral-500 dark:placeholder-neutral-400 focus:outline-none focus:border-neutral-300 dark:focus:border-neutral-600 transition-colors resize-none"
                    placeholder="Any specific requirements or questions?"
                  />
                </div>

                <button
                  type="submit"
                  disabled={!selectedSlot || isLoading}
                  className="w-full py-4 bg-neutral-900 dark:bg-neutral-100 hover:bg-neutral-800 dark:hover:bg-neutral-200 disabled:bg-neutral-300 dark:disabled:bg-neutral-700 text-neutral-100 dark:text-neutral-900 font-semibold rounded-2xl transition-all duration-200 transform hover:scale-[1.02] disabled:transform-none disabled:cursor-not-allowed flex items-center justify-center space-x-2"
                >
                  {isLoading ? (
                    <>
                      <div className="w-5 h-5 border-2 border-neutral-400 border-t-neutral-100 dark:border-t-neutral-900 rounded-full animate-spin"></div>
                      <span>Booking...</span>
                    </>
                  ) : (
                    <>
                      <span>Confirm Booking</span>
                      <ArrowRight className="w-5 h-5" />
                    </>
                  )}
                </button>

                {selectedSlot && (
                  <div className="mt-4 p-4 bg-neutral-50 dark:bg-neutral-800 rounded-xl">
                    <h4 className="font-medium text-neutral-900 dark:text-neutral-100 mb-2">
                      Selected Appointment:
                    </h4>
                    <p className="text-sm text-neutral-600 dark:text-neutral-400">
                      {new Date(selectedSlot.date).toLocaleDateString('en-US', { 
                        weekday: 'long', 
                        year: 'numeric', 
                        month: 'long', 
                        day: 'numeric' 
                      })} at {selectedSlot.time}
                    </p>
                  </div>
                )}
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CTAPage;

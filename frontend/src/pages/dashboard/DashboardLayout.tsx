/**
 * Dashboard Layout - Sidebar navigation with fancy names
 */
import { useState, useEffect } from 'react';
import { useNavigate, useLocation, Outlet } from 'react-router-dom';
import { ThemeToggle } from '../../components';

const DashboardLayout = () => {
  const [user, setUser] = useState<any>(null);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    const userData = localStorage.getItem('user');
    const isAuthenticated = localStorage.getItem('isAuthenticated');
    
    if (userData && isAuthenticated) {
      setUser(JSON.parse(userData));
    } else {
      navigate('/login');
    }
  }, [navigate]);

  const handleLogout = () => {
    localStorage.removeItem('isAuthenticated');
    localStorage.removeItem('user');
    navigate('/');
  };

  const menuItems = [
    { 
      id: 'nexus-hub', 
      name: 'Nexus Hub', 
      icon: '🏠', 
      description: 'Command Center',
      path: '/dashboard',
      gradient: 'from-blue-500 to-purple-600'
    },
    { 
      id: 'quantum-chat', 
      name: 'Quantum Chat', 
      icon: '💬', 
      description: 'AI Playground',
      path: '/dashboard/playground',
      gradient: 'from-purple-500 to-pink-600'
    },
    { 
      id: 'stellar-ops', 
      name: 'Stellar Ops', 
      icon: '⚡', 
      description: 'Operations Hub',
      path: '/dashboard/operations',
      gradient: 'from-orange-500 to-red-600'
    },
  ];

  const getCurrentPageName = () => {
    const currentItem = menuItems.find(item => item.path === location.pathname);
    return currentItem || menuItems[0];
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-white text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p>Loading NexusAI...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Sidebar */}
      <div className="w-80 bg-gradient-to-b from-gray-900 to-gray-800 shadow-2xl flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-gray-700">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
              <span className="text-xl">🚀</span>
            </div>
            <div>
              <h1 className="text-xl font-bold text-white">NexusAI</h1>
              <p className="text-sm text-gray-400">Command Center</p>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex-1 p-6">
          <nav className="space-y-3">
            {menuItems.map((item) => (
              <button
                key={item.id}
                onClick={() => navigate(item.path)}
                className={`w-full flex items-center space-x-4 p-4 rounded-xl transition-all duration-200 group ${
                  location.pathname === item.path
                    ? `bg-gradient-to-r ${item.gradient} text-white shadow-lg`
                    : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                }`}
              >
                <span className="text-2xl">{item.icon}</span>
                <div className="text-left">
                  <div className="font-semibold">{item.name}</div>
                  <div className={`text-sm ${
                    location.pathname === item.path ? 'text-white/80' : 'text-gray-500'
                  }`}>
                    {item.description}
                  </div>
                </div>
              </button>
            ))}
          </nav>
        </div>

        {/* User Profile & Logout */}
        <div className="p-6 border-t border-gray-700">
          <div className="flex items-center space-x-3 mb-4 p-3 bg-gray-700 rounded-xl">
            <div className="w-10 h-10 bg-gradient-to-r from-emerald-400 to-blue-500 rounded-lg flex items-center justify-center text-lg">
              {user.avatar}
            </div>
            <div className="flex-1">
              <div className="font-semibold text-white text-sm">{user.name}</div>
              <div className="text-xs text-gray-400">{user.role}</div>
            </div>
          </div>
          
          <div className="space-y-2">
            <button className="w-full flex items-center space-x-3 p-3 text-gray-300 hover:bg-gray-700 hover:text-white rounded-xl transition-all duration-200">
              <span>⚙️</span>
              <span className="text-sm">Profile Settings</span>
            </button>
            <button 
              onClick={handleLogout}
              className="w-full flex items-center space-x-3 p-3 text-gray-300 hover:bg-red-600 hover:text-white rounded-xl transition-all duration-200"
            >
              <span>🚪</span>
              <span className="text-sm">Sign Out</span>
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Top Bar */}
        <div className="bg-white shadow-sm border-b border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">
                {getCurrentPageName().name}
              </h2>
              <p className="text-gray-600">
                {getCurrentPageName().description}
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span>Online</span>
              </div>
              <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100">
                <span className="text-xl">🔔</span>
              </button>
            </div>
          </div>
        </div>

        {/* Content Area */}
        <div className="flex-1 p-6 overflow-auto">
          <Outlet />
        </div>
      </div>
    </div>
  );
};

export default DashboardLayout;

/**
 * Dashboard Page - Pinterest-inspired clean design
 * Reference: https://www.pinterest.com/pin/68748693039/
 */
import React, { useState, useEffect } from 'react';
import { 
  BarChart3, 
  MessageSquare, 
  Users, 
  Calendar, 
  TrendingUp, 
  Activity,
  Clock,
  CheckCircle,
  AlertCircle,
  Plus
} from 'lucide-react';
import { ThemeToggle } from '../../components';

interface DashboardStats {
  total_users: number;
  total_messages: number;
  total_bookings: number;
  active_sessions: number;
  today_bookings: number;
  today_messages: number;
}

interface AnalyticsData {
  date_range: Record<string, string>;
  message_analytics: Record<string, any>;
  booking_analytics: Record<string, any>;
  user_analytics: Record<string, any>;
}

const DashboardPage: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const token = localStorage.getItem('access_token');
      const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      };

      // Fetch dashboard stats
      const statsResponse = await fetch('http://localhost:8000/api/dashboard/stats', { headers });
      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData);
      }

      // Fetch analytics
      const analyticsResponse = await fetch('http://localhost:8000/api/dashboard/analytics?days=7', { headers });
      if (analyticsResponse.ok) {
        const analyticsData = await analyticsResponse.json();
        setAnalytics(analyticsData);
      }

    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError('Failed to load dashboard data');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-neutral-50 dark:bg-neutral-950 p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          {/* Header Skeleton */}
          <div className="flex justify-between items-center">
            <div className="h-8 bg-neutral-200 dark:bg-neutral-800 rounded w-48 animate-pulse"></div>
            <div className="h-10 bg-neutral-200 dark:bg-neutral-800 rounded w-32 animate-pulse"></div>
          </div>
          
          {/* Stats Grid Skeleton */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-neutral-200 dark:bg-neutral-800 rounded-2xl h-32 animate-pulse"></div>
            ))}
          </div>
          
          {/* Charts Skeleton */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-neutral-200 dark:bg-neutral-800 rounded-2xl h-80 animate-pulse"></div>
            <div className="bg-neutral-200 dark:bg-neutral-800 rounded-2xl h-80 animate-pulse"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-neutral-50 dark:bg-neutral-950 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <p className="text-neutral-600 dark:text-neutral-400 mb-4">{error}</p>
          <button 
            onClick={fetchDashboardData}
            className="px-6 py-2 bg-neutral-900 dark:bg-neutral-100 text-neutral-100 dark:text-neutral-900 rounded-xl hover:bg-neutral-800 dark:hover:bg-neutral-200 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  const statCards = [
    {
      title: 'Total Users',
      value: stats?.total_users || 0,
      icon: Users,
      change: '+12%',
      changeType: 'positive' as const,
      color: 'blue'
    },
    {
      title: 'Messages Today',
      value: stats?.today_messages || 0,
      icon: MessageSquare,
      change: '+8%',
      changeType: 'positive' as const,
      color: 'green'
    },
    {
      title: 'Active Sessions',
      value: stats?.active_sessions || 0,
      icon: Activity,
      change: '+15%',
      changeType: 'positive' as const,
      color: 'purple'
    },
    {
      title: 'Bookings Today',
      value: stats?.today_bookings || 0,
      icon: Calendar,
      change: '+5%',
      changeType: 'positive' as const,
      color: 'orange'
    }
  ];

  return (
    <div className="min-h-screen bg-neutral-50 dark:bg-neutral-950 transition-colors duration-300">
      <div className="max-w-7xl mx-auto p-6 space-y-8">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-semibold text-neutral-900 dark:text-neutral-100">
              Dashboard
            </h1>
            <p className="text-neutral-600 dark:text-neutral-400 mt-1">
              Welcome back! Here's what's happening today.
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <ThemeToggle />
            <button className="flex items-center space-x-2 px-4 py-2 bg-neutral-900 dark:bg-neutral-100 text-neutral-100 dark:text-neutral-900 rounded-xl hover:bg-neutral-800 dark:hover:bg-neutral-200 transition-all duration-200">
              <Plus className="w-4 h-4" />
              <span className="font-medium">New</span>
            </button>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {statCards.map((card, index) => (
            <div
              key={card.title}
              className="bg-white dark:bg-neutral-900 rounded-2xl p-6 border border-neutral-200 dark:border-neutral-800 hover:shadow-lg dark:hover:shadow-neutral-900/20 transition-all duration-200 animate-fade-in-up"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="flex items-center justify-between mb-4">
                <div className={`p-2 rounded-xl bg-${card.color}-100 dark:bg-${card.color}-900/20`}>
                  <card.icon className={`w-5 h-5 text-${card.color}-600 dark:text-${card.color}-400`} />
                </div>
                <span className={`text-sm font-medium ${
                  card.changeType === 'positive' 
                    ? 'text-green-600 dark:text-green-400' 
                    : 'text-red-600 dark:text-red-400'
                }`}>
                  {card.change}
                </span>
              </div>
              <div>
                <p className="text-2xl font-bold text-neutral-900 dark:text-neutral-100">
                  {card.value.toLocaleString()}
                </p>
                <p className="text-sm text-neutral-600 dark:text-neutral-400 mt-1">
                  {card.title}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Message Analytics */}
          <div className="bg-white dark:bg-neutral-900 rounded-2xl p-6 border border-neutral-200 dark:border-neutral-800 animate-fade-in-up" style={{ animationDelay: '0.5s' }}>
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-neutral-900 dark:text-neutral-100">
                Message Activity
              </h3>
              <BarChart3 className="w-5 h-5 text-neutral-400" />
            </div>
            <div className="h-64 flex items-center justify-center text-neutral-500 dark:text-neutral-400">
              <div className="text-center">
                <BarChart3 className="w-12 h-12 mx-auto mb-2 opacity-50" />
                <p>Chart visualization coming soon</p>
              </div>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="bg-white dark:bg-neutral-900 rounded-2xl p-6 border border-neutral-200 dark:border-neutral-800 animate-fade-in-up" style={{ animationDelay: '0.6s' }}>
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-neutral-900 dark:text-neutral-100">
                Recent Activity
              </h3>
              <Clock className="w-5 h-5 text-neutral-400" />
            </div>
            <div className="space-y-4">
              {[
                { action: 'New user registered', time: '2 minutes ago', type: 'user' },
                { action: 'Booking confirmed', time: '5 minutes ago', type: 'booking' },
                { action: 'Message sent', time: '8 minutes ago', type: 'message' },
                { action: 'Session started', time: '12 minutes ago', type: 'session' }
              ].map((activity, index) => (
                <div key={index} className="flex items-center space-x-3 p-3 rounded-xl hover:bg-neutral-50 dark:hover:bg-neutral-800 transition-colors">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-neutral-900 dark:text-neutral-100">
                      {activity.action}
                    </p>
                    <p className="text-xs text-neutral-500 dark:text-neutral-400">
                      {activity.time}
                    </p>
                  </div>
                  <CheckCircle className="w-4 h-4 text-green-500" />
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white dark:bg-neutral-900 rounded-2xl p-6 border border-neutral-200 dark:border-neutral-800 animate-fade-in-up" style={{ animationDelay: '0.7s' }}>
          <h3 className="text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-6">
            Quick Actions
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[
              { title: 'View Messages', description: 'Check recent conversations', icon: MessageSquare },
              { title: 'Manage Bookings', description: 'Review appointments', icon: Calendar },
              { title: 'User Analytics', description: 'View user insights', icon: TrendingUp }
            ].map((action, index) => (
              <button
                key={action.title}
                className="p-4 text-left rounded-xl border border-neutral-200 dark:border-neutral-700 hover:border-neutral-300 dark:hover:border-neutral-600 hover:bg-neutral-50 dark:hover:bg-neutral-800 transition-all duration-200 group"
              >
                <action.icon className="w-6 h-6 text-neutral-600 dark:text-neutral-400 mb-3 group-hover:text-neutral-900 dark:group-hover:text-neutral-100 transition-colors" />
                <h4 className="font-medium text-neutral-900 dark:text-neutral-100 mb-1">
                  {action.title}
                </h4>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  {action.description}
                </p>
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;

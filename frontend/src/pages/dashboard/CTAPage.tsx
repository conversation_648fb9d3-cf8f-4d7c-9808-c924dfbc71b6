/**
 * CTA Page - Stellar Ops (Operations & Management)
 */
import { useState } from 'react';

const CTAPage = () => {
  const [activeTab, setActiveTab] = useState('operations');
  const [notifications] = useState([
    { id: 1, type: 'booking', message: 'New ticket booking from WhatsApp', time: '2 min ago', priority: 'high' },
    { id: 2, type: 'agent', message: 'Agent <PERSON> needs assistance with customer', time: '5 min ago', priority: 'medium' },
    { id: 3, type: 'system', message: 'System maintenance scheduled for tonight', time: '1 hour ago', priority: 'low' },
    { id: 4, type: 'booking', message: 'Booking confirmation sent to customer', time: '2 hours ago', priority: 'low' },
  ]);

  const [agents] = useState([
    { id: 1, name: '<PERSON>', status: 'online', activeChats: 5, platform: 'WhatsApp', avatar: '👩‍💼' },
    { id: 2, name: '<PERSON>', status: 'busy', activeChats: 8, platform: 'Facebook', avatar: '👨‍💼' },
    { id: 3, name: '<PERSON>', status: 'online', activeChats: 3, platform: 'Instagram', avatar: '👩‍💻' },
    { id: 4, name: '<PERSON>', status: 'away', activeChats: 0, platform: 'General', avatar: '👨‍💻' },
  ]);

  const [bookings] = useState([
    { id: 1, customer: 'John Doe', event: 'Comedy Show', date: '2024-01-15', status: 'confirmed', platform: 'WhatsApp' },
    { id: 2, customer: 'Jane Smith', event: 'Music Concert', date: '2024-01-16', status: 'pending', platform: 'Facebook' },
    { id: 3, customer: 'Bob Johnson', event: 'Theater Play', date: '2024-01-17', status: 'confirmed', platform: 'Instagram' },
    { id: 4, customer: 'Alice Brown', event: 'Stand-up Comedy', date: '2024-01-18', status: 'cancelled', platform: 'WhatsApp' },
  ]);

  const tabs = [
    { id: 'operations', name: 'Operations', icon: '⚡', description: 'Live Operations' },
    { id: 'agents', name: 'Agent Hub', icon: '👥', description: 'Team Management' },
    { id: 'bookings', name: 'Booking Center', icon: '🎫', description: 'Ticket Management' },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-orange-600 to-red-600 rounded-2xl p-8 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">Stellar Ops ⚡</h1>
            <p className="text-orange-100">Operations Command & Control Center</p>
          </div>
          <div className="text-6xl opacity-20">⚙️</div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-100">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-2 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-orange-500 text-orange-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center space-x-2">
                  <span className="text-lg">{tab.icon}</span>
                  <span>{tab.name}</span>
                </div>
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {/* Operations Tab */}
          {activeTab === 'operations' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Live Notifications */}
                <div className="bg-gray-50 rounded-xl p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-gray-900">Live Notifications</h3>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                      <span className="text-sm text-gray-600">Live</span>
                    </div>
                  </div>
                  <div className="space-y-3">
                    {notifications.map((notification) => (
                      <div key={notification.id} className="bg-white p-4 rounded-lg border border-gray-200">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-1">
                              <span className={`w-2 h-2 rounded-full ${
                                notification.priority === 'high' ? 'bg-red-500' :
                                notification.priority === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
                              }`}></span>
                              <span className="text-sm font-medium text-gray-900 capitalize">{notification.type}</span>
                            </div>
                            <p className="text-sm text-gray-600">{notification.message}</p>
                            <p className="text-xs text-gray-400 mt-1">{notification.time}</p>
                          </div>
                          <button className="text-gray-400 hover:text-gray-600">
                            <span className="text-lg">×</span>
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* System Status */}
                <div className="bg-gray-50 rounded-xl p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">System Status</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-3 bg-white rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                        <span className="text-sm font-medium">WhatsApp API</span>
                      </div>
                      <span className="text-sm text-green-600">Operational</span>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-white rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                        <span className="text-sm font-medium">Facebook Messenger</span>
                      </div>
                      <span className="text-sm text-green-600">Operational</span>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-white rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                        <span className="text-sm font-medium">Instagram API</span>
                      </div>
                      <span className="text-sm text-yellow-600">Degraded</span>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-white rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                        <span className="text-sm font-medium">AI Engine</span>
                      </div>
                      <span className="text-sm text-green-600">Operational</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Agents Tab */}
          {activeTab === 'agents' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {agents.map((agent) => (
                  <div key={agent.id} className="bg-gray-50 rounded-xl p-6">
                    <div className="text-center">
                      <div className="text-4xl mb-3">{agent.avatar}</div>
                      <h4 className="font-semibold text-gray-900">{agent.name}</h4>
                      <div className="flex items-center justify-center space-x-2 mt-2">
                        <div className={`w-2 h-2 rounded-full ${
                          agent.status === 'online' ? 'bg-green-500' :
                          agent.status === 'busy' ? 'bg-red-500' : 'bg-yellow-500'
                        }`}></div>
                        <span className="text-sm text-gray-600 capitalize">{agent.status}</span>
                      </div>
                      <div className="mt-4 space-y-2">
                        <div className="text-sm text-gray-600">
                          <span className="font-medium">{agent.activeChats}</span> active chats
                        </div>
                        <div className="text-xs text-gray-500">{agent.platform}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Bookings Tab */}
          {activeTab === 'bookings' && (
            <div className="space-y-6">
              <div className="bg-gray-50 rounded-xl overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="min-w-full">
                    <thead className="bg-gray-100">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Platform</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {bookings.map((booking) => (
                        <tr key={booking.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {booking.customer}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {booking.event}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {booking.date}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {booking.platform}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              booking.status === 'confirmed' ? 'bg-green-100 text-green-800' :
                              booking.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {booking.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <button className="text-orange-600 hover:text-orange-900 mr-3">Edit</button>
                            <button className="text-red-600 hover:text-red-900">Cancel</button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CTAPage;

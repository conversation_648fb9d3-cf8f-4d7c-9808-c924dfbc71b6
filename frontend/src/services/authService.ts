/**
 * Authentication service
 * Handles login, logout, registration, and token management
 */
import { httpBase } from './baseHttp';
import type { ApiCallbacks } from './baseHttp';

// Define types locally to avoid import issues
interface LoginRequest {
  email: string;
  password: string;
}

interface RegisterRequest {
  name: string;
  email: string;
  phone: string;
  password: string;
}

interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  is_active: boolean;
  created_at: string;
  last_login?: string;
  preferences: Record<string, any>;
}

interface AuthResponse {
  success: boolean;
  message: string;
  user?: User;
  access_token?: string;
  token_type?: string;
}

class AuthService {
  private readonly AUTH_ENDPOINTS = {
    LOGIN: '/api/auth/login',
    REGISTER: '/api/auth/register',
    ME: '/api/auth/me',
    CHECK: '/api/auth/check',
  };

  /**
   * Login user with email and password
   */
  async login(
    credentials: LoginRequest,
    callbacks?: ApiCallbacks<AuthResponse>
  ): Promise<AuthResponse | null> {
    try {
      const response = await httpBase.post<AuthResponse>(
        this.AUTH_ENDPOINTS.LOGIN,
        credentials,
        {},
        {
          onSuccess: (data) => {
            if (data.success && data.access_token && data.user) {
              // Store token and user data
              httpBase.setAuthToken(data.access_token);
              localStorage.setItem('user', JSON.stringify(data.user));
              console.log('✅ Login successful:', data.user.name);
            }
            callbacks?.onSuccess?.(data);
          },
          onFailure: (error) => {
            console.error('❌ Login failed:', error.message);
            callbacks?.onFailure?.(error);
          },
          onFinally: callbacks?.onFinally,
        }
      );

      return response;
    } catch (error) {
      return null;
    }
  }

  /**
   * Register new user
   */
  async register(
    userData: RegisterRequest,
    callbacks?: ApiCallbacks<AuthResponse>
  ): Promise<AuthResponse | null> {
    try {
      const response = await httpBase.post<AuthResponse>(
        this.AUTH_ENDPOINTS.REGISTER,
        userData,
        {},
        {
          onSuccess: (data) => {
            if (data.success && data.access_token && data.user) {
              // Store token and user data
              httpBase.setAuthToken(data.access_token);
              localStorage.setItem('user', JSON.stringify(data.user));
              console.log('✅ Registration successful:', data.user.name);
            }
            callbacks?.onSuccess?.(data);
          },
          onFailure: (error) => {
            console.error('❌ Registration failed:', error.message);
            callbacks?.onFailure?.(error);
          },
          onFinally: callbacks?.onFinally,
        }
      );

      return response;
    } catch (error) {
      return null;
    }
  }

  /**
   * Logout current user
   */
  async logout(callbacks?: ApiCallbacks<{ message: string }>): Promise<boolean> {
    try {
      // Clear token and user data
      httpBase.clearAuthToken();
      console.log('✅ Logout successful');
      callbacks?.onSuccess?.({ message: 'Logged out successfully' });
      return true;
    } catch (error) {
      console.error('❌ Logout failed:', error);
      callbacks?.onFailure?.({ message: 'Logout failed', details: error });
      return false;
    } finally {
      callbacks?.onFinally?.();
    }
  }

  /**
   * Get current user profile
   */
  async getCurrentUser(callbacks?: ApiCallbacks<User>): Promise<User | null> {
    try {
      const response = await httpBase.get<User>(
        this.AUTH_ENDPOINTS.ME,
        {},
        {
          onSuccess: (data) => {
            // Update stored user data
            localStorage.setItem('user', JSON.stringify(data));
            console.log('✅ User profile loaded:', data.name);
            callbacks?.onSuccess?.(data);
          },
          onFailure: (error) => {
            console.error('❌ Failed to load user profile:', error.message);
            callbacks?.onFailure?.(error);
          },
          onFinally: callbacks?.onFinally,
        }
      );

      return response;
    } catch (error) {
      return null;
    }
  }

  /**
   * Check authentication status
   */
  async checkAuth(callbacks?: ApiCallbacks<{ authenticated: boolean; user?: User }>): Promise<{ authenticated: boolean; user?: User } | null> {
    try {
      const response = await httpBase.get<{ authenticated: boolean; user?: User }>(
        this.AUTH_ENDPOINTS.CHECK,
        {},
        {
          onSuccess: (data) => {
            if (data.authenticated && data.user) {
              localStorage.setItem('user', JSON.stringify(data.user));
            }
            console.log('✅ Auth check completed');
            callbacks?.onSuccess?.(data);
          },
          onFailure: (error) => {
            console.error('❌ Auth check failed:', error.message);
            callbacks?.onFailure?.(error);
          },
          onFinally: callbacks?.onFinally,
        }
      );

      return response;
    } catch (error) {
      return null;
    }
  }

  /**
   * Check if user is authenticated (client-side check)
   */
  isAuthenticated(): boolean {
    return httpBase.isAuthenticated();
  }

  /**
   * Get stored user data
   */
  getStoredUser(): User | null {
    try {
      const userData = localStorage.getItem('user');
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Failed to parse stored user data:', error);
      return null;
    }
  }

  /**
   * Validate email format
   */
  validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate password strength
   */
  validatePassword(password: string): { isValid: boolean; message: string } {
    if (password.length < 8) {
      return { isValid: false, message: 'Password must be at least 8 characters long' };
    }
    if (!/(?=.*[a-z])/.test(password)) {
      return { isValid: false, message: 'Password must contain at least one lowercase letter' };
    }
    if (!/(?=.*[A-Z])/.test(password)) {
      return { isValid: false, message: 'Password must contain at least one uppercase letter' };
    }
    if (!/(?=.*\d)/.test(password)) {
      return { isValid: false, message: 'Password must contain at least one number' };
    }
    return { isValid: true, message: 'Password is valid' };
  }

  /**
   * Validate phone number format
   */
  validatePhone(phone: string): boolean {
    const phoneRegex = /^\+?[\d\s\-\(\)]{10,15}$/;
    return phoneRegex.test(phone.replace(/\s/g, ''));
  }
}

// Export singleton instance
export const authService = new AuthService();
export default authService;

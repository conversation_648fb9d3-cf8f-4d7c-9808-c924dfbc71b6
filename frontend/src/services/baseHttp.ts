/**
 * Base HTTP service with Axios configuration
 * Handles API calls with proper error handling and callbacks
 */
import axios from 'axios';
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

// Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
const API_TIMEOUT = parseInt(import.meta.env.VITE_API_TIMEOUT) || 10000;

// Callback types for success, failure, and finally blocks
export interface ApiCallbacks<T = any> {
  onSuccess?: (data: T) => void;
  onFailure?: (error: ApiError) => void;
  onFinally?: () => void;
}

// Custom error type
export interface ApiError {
  message: string;
  status?: number;
  code?: string;
  details?: any;
}

// Create Axios instance
const httpClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: API_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true, // For session-based auth
});

// Request interceptor
httpClient.interceptors.request.use(
  (config) => {
    // Add JWT token to headers if available
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    if (import.meta.env.VITE_DEV_MODE === 'true') {
      console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);
    }
    return config;
  },
  (error) => {
    console.error('❌ Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor
httpClient.interceptors.response.use(
  (response: AxiosResponse) => {
    if (import.meta.env.VITE_DEV_MODE === 'true') {
      console.log(`✅ API Response: ${response.status} ${response.config.url}`);
    }
    return response;
  },
  (error) => {
    const apiError: ApiError = {
      message: 'An unexpected error occurred',
      status: error.response?.status,
      code: error.code,
      details: error.response?.data,
    };

    if (error.response) {
      // Handle 401 Unauthorized - clear token and redirect to login
      if (error.response.status === 401) {
        localStorage.removeItem('access_token');
        localStorage.removeItem('user');
        // Only redirect if not already on login page
        if (window.location.pathname !== '/login') {
          window.location.href = '/login';
        }
      }

      // Server responded with error status
      apiError.message = error.response.data?.detail || error.response.data?.message || `Server Error: ${error.response.status}`;
    } else if (error.request) {
      // Request was made but no response received
      apiError.message = 'Network error - please check your connection';
    } else {
      // Something else happened
      apiError.message = error.message || 'Request failed';
    }

    console.error('❌ API Error:', apiError);
    return Promise.reject(apiError);
  }
);

// Base HTTP service class
class BaseHttpService {
  private client = httpClient;

  /**
   * Generic API call method with callbacks
   */
  async apiCall<T = any>(
    config: AxiosRequestConfig,
    callbacks?: ApiCallbacks<T>
  ): Promise<T | null> {
    try {
      const response = await this.client.request<T>(config);
      const data = response.data;
      
      callbacks?.onSuccess?.(data);
      return data;
    } catch (error) {
      const apiError = error as ApiError;
      callbacks?.onFailure?.(apiError);
      throw apiError;
    } finally {
      callbacks?.onFinally?.();
    }
  }

  /**
   * GET request
   */
  async get<T = any>(
    url: string,
    config?: AxiosRequestConfig,
    callbacks?: ApiCallbacks<T>
  ): Promise<T | null> {
    return this.apiCall<T>({ ...config, method: 'GET', url }, callbacks);
  }

  /**
   * POST request
   */
  async post<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig,
    callbacks?: ApiCallbacks<T>
  ): Promise<T | null> {
    return this.apiCall<T>({ ...config, method: 'POST', url, data }, callbacks);
  }

  /**
   * PUT request
   */
  async put<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig,
    callbacks?: ApiCallbacks<T>
  ): Promise<T | null> {
    return this.apiCall<T>({ ...config, method: 'PUT', url, data }, callbacks);
  }

  /**
   * DELETE request
   */
  async delete<T = any>(
    url: string,
    config?: AxiosRequestConfig,
    callbacks?: ApiCallbacks<T>
  ): Promise<T | null> {
    return this.apiCall<T>({ ...config, method: 'DELETE', url }, callbacks);
  }

  /**
   * PATCH request
   */
  async patch<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig,
    callbacks?: ApiCallbacks<T>
  ): Promise<T | null> {
    return this.apiCall<T>({ ...config, method: 'PATCH', url, data }, callbacks);
  }

  /**
   * Get the base URL
   */
  getBaseUrl(): string {
    return API_BASE_URL;
  }

  /**
   * Update default headers
   */
  setDefaultHeader(key: string, value: string): void {
    this.client.defaults.headers.common[key] = value;
  }

  /**
   * Remove default header
   */
  removeDefaultHeader(key: string): void {
    delete this.client.defaults.headers.common[key];
  }

  /**
   * Set authentication token
   */
  setAuthToken(token: string): void {
    localStorage.setItem('access_token', token);
    this.client.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  }

  /**
   * Clear authentication token
   */
  clearAuthToken(): void {
    localStorage.removeItem('access_token');
    localStorage.removeItem('user');
    delete this.client.defaults.headers.common['Authorization'];
  }

  /**
   * Get current auth token
   */
  getAuthToken(): string | null {
    return localStorage.getItem('access_token');
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return !!this.getAuthToken();
  }
}

// Export singleton instance
export const httpBase = new BaseHttpService();
export default httpBase;

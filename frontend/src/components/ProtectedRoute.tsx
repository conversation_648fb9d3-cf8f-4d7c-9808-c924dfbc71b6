/**
 * Protected Route component
 * Redirects to login if user is not authenticated
 */
import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { checkAuth, selectAuth } from '../store/authSlice';
import Loading from './Loading';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const dispatch = useAppDispatch();
  const location = useLocation();
  const { isAuthenticated, isLoading, user } = useAppSelector(selectAuth);
  const [isInitializing, setIsInitializing] = useState(true);

  // Check authentication status on mount
  useEffect(() => {
    const checkAuthStatus = async () => {
      if (!user && !isAuthenticated) {
        try {
          await dispatch(checkAuth());
        } catch (error) {
          console.log('User not authenticated');
        }
      }
      setIsInitializing(false);
    };

    checkAuthStatus();
  }, [dispatch, user, isAuthenticated]);

  // Show loading while checking authentication
  if (isInitializing || isLoading) {
    return (
      <Loading
        fullScreen
        text="Checking authentication..."
        variant="spinner"
        size="lg"
      />
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return (
      <Navigate
        to="/login"
        state={{ from: location }}
        replace
      />
    );
  }

  // Render protected content
  return <>{children}</>;
};

export default ProtectedRoute;

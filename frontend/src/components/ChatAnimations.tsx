/**
 * Chat Animation Components for Message Sending/Receiving
 */
import React, { useState, useEffect } from 'react';
import { Send } from 'lucide-react';

interface MessageAnimationProps {
  message: string;
  onAnimationComplete?: () => void;
  isUser?: boolean;
}

export const MessageSendAnimation: React.FC<MessageAnimationProps> = ({
  message,
  onAnimationComplete,
  isUser = true
}) => {
  const [stage, setStage] = useState<'typing' | 'sending' | 'sent'>('typing');

  useEffect(() => {
    const timer1 = setTimeout(() => setStage('sending'), 500);
    const timer2 = setTimeout(() => setStage('sent'), 1000);
    const timer3 = setTimeout(() => onAnimationComplete?.(), 1500);

    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
      clearTimeout(timer3);
    };
  }, [onAnimationComplete]);

  return (
    <div className="flex justify-end mb-4">
      <div className={`
        max-w-xs lg:max-w-md px-4 py-3 rounded-2xl
        ${isUser ? 'bg-primary-500 text-white' : 'bg-neutral-200 dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100'}
        transition-all duration-300 ease-apple
        ${stage === 'typing' ? 'scale-95 opacity-70' : ''}
        ${stage === 'sending' ? 'scale-100 opacity-100 animate-bounce-soft' : ''}
        ${stage === 'sent' ? 'scale-100 opacity-100' : ''}
      `}>
        <div className="flex items-center space-x-2">
          <span className="text-sm">{message}</span>
          {stage === 'sending' && (
            <Send className="w-4 h-4 animate-pulse" />
          )}
          {stage === 'sent' && (
            <div className="flex space-x-1">
              <div className="w-1 h-1 bg-current rounded-full opacity-60"></div>
              <div className="w-1 h-1 bg-current rounded-full opacity-60"></div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export const MessageReceiveAnimation: React.FC<MessageAnimationProps> = ({
  message,
  onAnimationComplete,
  isUser = false
}) => {
  const [stage, setStage] = useState<'hidden' | 'typing' | 'appearing' | 'visible'>('hidden');
  const [displayedText, setDisplayedText] = useState('');

  useEffect(() => {
    const timer1 = setTimeout(() => setStage('typing'), 300);
    const timer2 = setTimeout(() => setStage('appearing'), 1500);
    
    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
    };
  }, []);

  useEffect(() => {
    if (stage === 'appearing') {
      let index = 0;
      const typeTimer = setInterval(() => {
        if (index < message.length) {
          setDisplayedText(message.slice(0, index + 1));
          index++;
        } else {
          clearInterval(typeTimer);
          setStage('visible');
          onAnimationComplete?.();
        }
      }, 30);

      return () => clearInterval(typeTimer);
    }
  }, [stage, message, onAnimationComplete]);

  if (stage === 'hidden') return null;

  return (
    <div className="flex justify-start mb-4">
      <div className={`
        max-w-xs lg:max-w-md px-4 py-3 rounded-2xl
        bg-neutral-100 dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100
        transition-all duration-300 ease-apple
        ${stage === 'typing' ? 'animate-pulse' : ''}
        ${stage === 'appearing' || stage === 'visible' ? 'animate-fade-in-up' : ''}
      `}>
        {stage === 'typing' && (
          <div className="flex items-center space-x-1">
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-neutral-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
              <div className="w-2 h-2 bg-neutral-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
              <div className="w-2 h-2 bg-neutral-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
            </div>
            <span className="text-xs text-neutral-500 ml-2">AI is typing...</span>
          </div>
        )}
        
        {(stage === 'appearing' || stage === 'visible') && (
          <div className="text-sm">
            {displayedText}
            {stage === 'appearing' && (
              <span className="inline-block w-2 h-4 bg-primary-500 ml-1 animate-pulse"></span>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export const TypingIndicator: React.FC = () => {
  return (
    <div className="flex justify-start mb-4 animate-fade-in">
      <div className="bg-neutral-100 dark:bg-neutral-800 px-4 py-3 rounded-2xl">
        <div className="flex items-center space-x-1">
          <div className="flex space-x-1">
            <div className="w-2 h-2 bg-neutral-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
            <div className="w-2 h-2 bg-neutral-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
            <div className="w-2 h-2 bg-neutral-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
          </div>
          <span className="text-xs text-neutral-500 ml-2">AI is thinking...</span>
        </div>
      </div>
    </div>
  );
};

// Hook for managing chat animations
export const useChatAnimations = () => {
  const [animatingMessage, setAnimatingMessage] = useState<string | null>(null);
  const [showTyping, setShowTyping] = useState(false);

  const sendMessage = (message: string) => {
    setAnimatingMessage(message);
    
    // After send animation, show typing indicator
    setTimeout(() => {
      setAnimatingMessage(null);
      setShowTyping(true);
    }, 1500);
  };

  const receiveMessage = () => {
    setShowTyping(false);
  };

  return {
    animatingMessage,
    showTyping,
    sendMessage,
    receiveMessage
  };
};

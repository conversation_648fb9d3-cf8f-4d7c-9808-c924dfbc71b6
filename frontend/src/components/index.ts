/**
 * Components barrel file
 * Exports all components for clean imports
 */

// Core UI components
export { default as Button } from './Button';
export type { ButtonProps } from './Button';

export { default as Input } from './Input';
export type { InputProps } from './Input';

export { default as Card, CardHeader, CardContent, CardFooter } from './Card';
export type { CardProps, CardHeaderProps, CardContentProps, CardFooterProps } from './Card';

export { default as Modal, ModalHeader, ModalBody, ModalFooter } from './Modal';
export type { ModalProps, ModalHeaderProps, ModalBodyProps, ModalFooterProps } from './Modal';

export { default as Loading, Skeleton, LoadingOverlay } from './Loading';
export type { LoadingProps, SkeletonProps, LoadingOverlayProps } from './Loading';

export { default as ProtectedRoute } from './ProtectedRoute';

export { default as ThemeToggle } from './ThemeToggle';

export {
  MessageSendAnimation,
  MessageReceiveAnimation,
  TypingIndicator,
  useChatAnimations
} from './ChatAnimations';

// API Response Types based on backend models

// Common types
export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
}

// User types
export interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  is_active: boolean;
  created_at: string;
  last_login?: string;
  preferences: Record<string, any>;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  name: string;
  email: string;
  phone: string;
  password: string;
}

export interface AuthResponse {
  success: boolean;
  message: string;
  user?: User;
  access_token?: string;
  token_type?: string;
}

// Chat types
export interface ChatMessage {
  content: string;
  message_type: 'user' | 'assistant';
  timestamp: string;
  analytics?: Record<string, any>;
}

export interface ChatRequest {
  message: string;
  session_id?: string;
  user_id?: string;
}

export interface ChatResponse {
  response: string;
  session_id: string;
  user_message_id: string;
  assistant_message_id: string;
  response_time_ms: number;
  user_analytics?: Record<string, any>;
  assistant_analytics?: Record<string, any>;
}

export interface ConversationHistory {
  session_id: string;
  messages: ChatMessage[];
  total_messages: number;
}

export interface SessionListResponse {
  sessions: Array<{ session_id: string; last_message: string }>;
  total: number;
}

export interface ConversationListResponse {
  conversations: Array<any>;
  total: number;
}

export interface ConversationCreateRequest {
  title?: string;
  initial_message?: string;
}

export interface ConversationUpdateRequest {
  title?: string;
  status?: string;
}

// Booking types
export type ServiceType = 'general_consultation' | 'technical_support' | 'course_enrollment' | 'loksewa_preparation' | 'career_guidance';
export type BookingStatus = 'pending' | 'confirmed' | 'cancelled' | 'completed' | 'no_show';

export interface TimeSlot {
  id: string;
  date: string;
  time: string;
  datetime: string;
  available: boolean;
  max_bookings: number;
  current_bookings: number;
  available_spots: number;
}

export interface BookingRequest {
  name: string;
  email: string;
  phone: string;
  service_type: ServiceType;
  date: string;
  time: string;
  notes?: string;
  session_id: string;
}

export interface Booking {
  id: string;
  booking_id: string;
  name: string;
  email: string;
  phone: string;
  service_type: ServiceType;
  date: string;
  time: string;
  notes?: string;
  status: BookingStatus;
  created_at: string;
}

export interface BookingResponse extends Booking {}

export interface BookingListResponse {
  bookings: BookingResponse[];
  total: number;
}

export interface BookingUpdate {
  status?: BookingStatus;
  notes?: string;
}

// Dashboard types
export interface DashboardStats {
  total_users: number;
  total_messages: number;
  total_bookings: number;
  active_sessions: number;
  today_bookings: number;
  today_messages: number;
}

export interface UserAnalytics {
  user_id: string;
  analytics: Record<string, any>;
}

export interface UserAnalyticsResponse {
  user_id: string;
  analytics: Record<string, any>;
}

export interface AnalyticsSummary {
  date_range: Record<string, string>;
  message_analytics: Record<string, any>;
  booking_analytics: Record<string, any>;
  user_analytics: Record<string, any>;
}

export interface SystemMetrics {
  database_status: string;
  collections_info: Record<string, any>;
  performance_metrics: Record<string, any>;
}

// Health check
export interface HealthResponse {
  status: string;
  timestamp: string;
  version: string;
  database_connected: boolean;
}
